<?php
ini_set("memory_limit", "2024M");
!defined("PROJECT_ID") && define("PROJECT_ID", 1111111);
require_once 'auto_load.php';

$app = CoreApp::create();

require_once $app->getDocumentRoot() . '/app/console/libopt.php';
require_once $app->getDocumentRoot() . "/app/console/export_global.php";


define('ROOT_PATH', ConfigLoader::getConfig("ROOT_PATH"));

Constants::$IS_CONSOLE_MODE = true;
Constants::$APP_NAME = 'console';
Constants::$IS_BACKEND_MODE = true;

function color_echo($msg, $fg = '', $bg = '', $ctrl = '')
{/*{{{*/
    $color_fg          = '';
    $color_bg          = '';
    $ansi_ctr          = 0;
    $color_close       = "\033[0m";
    $color_start_left  = "\033[";
    $color_start_right = "m";
    $ansi_ctr_left     = "\033[";
    $ansi_ctr_right    = "m";

    $next_line = ($msg[strlen($msg) - 1] == "\n") ? true : false;
    $message   = rtrim($msg, "\n");

    static $bgcolor_map = [
        'red'    => 41,
        'black'  => 40,
        'green'  => 42,
        'yellow' => 43,
        'blue'   => 44,
        'purple' => 45,
        'white'  => 47,
    ];

    static $fgcolor_map = [
        'black'  => 30,
        'red'    => 31,
        'green'  => 32,
        'yellow' => 33,
        'blue'   => 34,
        'purple' => 35,
        'white'  => 37,
    ];

    static $control = [
        'default'   => 0,
        'highlight' => 1,
        'underline' => 4,
        'blink'     => 5,
        'negative'  => 7,
        'conceal'   => 8,
    ];

    $color_fg = isset($fgcolor_map[$fg]) ? $fgcolor_map[$fg] : $fgcolor_map['white'];
    $color_bg = isset($bgcolor_map[$bg]) ? $bgcolor_map[$bg] : $bgcolor_map['black'];
    $ansi_ctr = isset($control[$ctrl]) ? $control[$ctrl] : 0;

    $ansi_ctr_str = '';
    if ($ansi_ctr) {
        $ansi_ctr_str = $ansi_ctr_left . $ansi_ctr . $ansi_ctr_right;
    }

    $color_message = $color_start_left . $color_fg . ';' . $color_bg . $color_start_right . $ansi_ctr_str . $message .
        $color_close . ($next_line ? "\n" : "");
    echo $color_message;
}

$narumiya_brand_id_arr = [6726,6757,6735,6739,6737,6736,6738,6771,6770,6911,6910,6569];
$leifu_brand_id_arr = [6778, 6841, 6850, 6935, 6934, 6964, 7190, 7189];
